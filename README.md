# App de Login - Kotlin Compose Desktop

Aplicação de login desenvolvida em Kotlin usando Jetpack Compose Desktop, com integração ao backend em `http://localhost:3000`.

## Estrutura do Projeto

```
mobile-test/
├── src/
│   ├── main/kotlin/
│   │   └── Main.kt
│   └── test/kotlin/
│       └── MainTest.kt
├── build.gradle.kts
├── settings.gradle.kts
├── gradle.properties
└── README.md
```

## Como Executar

### Executar o projeto:

```bash ./gradlew run

```

### Executar os testes:

```bash
./gradlew test
```

### Compilar o projeto:

```bash
./gradlew build
```

## Características

-   **Linguagem**: Kotlin 1.8.20
-   **JDK**: Java 17+
-   **Build Tool**: Gradle
-   **UI**: Jetpack Compose Desktop
-   **HTTP Client**: Ktor Client
-   **Serialização**: Kotlinx Serialization
-   **Testes**: JUnit Platform com kotlin-test
-   **Backend**: API REST em http://localhost:3000

## Configuração do Backend

Certifique-se de que o backend esteja rodando em `http://localhost:3000` antes de executar a aplicação.

### Endpoints utilizados:

-   `POST /auth/login` - Login com username/email e senha
-   Documentação completa: `http://localhost:3000/api`

## Configurações de Desenvolvimento

### Usar serviços mock (sem backend):

```bash
./gradlew run -Duse.mock=true
```

### Modo debug (mostra configurações):

```bash
./gradlew run -Ddebug=true
```

### URL customizada do backend:

```bash
./gradlew run -Dapi.base.url=http://localhost:8080
```

## Próximos Passos

1. Adicione mais classes e funcionalidades no diretório `src/main/kotlin/`
2. Escreva testes correspondentes em `src/test/kotlin/`
3. Configure dependências adicionais no `build.gradle.kts` conforme necessário
