package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import ui.theme.AppColors

/**
 * Interface para propriedades da tela de registro
 * Segue o padrão IHandleRegisterScreenProps
 */
interface IHandleRegisterScreenProps {
    val onNavigateBack: () -> Unit
    val onRegister: (username: String, email: String, password: String, fullName: String) -> Unit
    val isLoading: Boolean
    val errorMessage: String?
}

/**
 * Implementação das propriedades da tela de registro
 */
data class RegisterScreenProps(
    override val onNavigateBack: () -> Unit = {},
    override val onRegister: (username: String, email: String, password: String, fullName: String) -> Unit = { _, _, _, _ -> },
    override val isLoading: Boolean = false,
    override val errorMessage: String? = null
) : IHandleRegisterScreenProps

/**
 * Tela de Registro Mobile - Estilo Manga/Manhwa
 * Responsabilidade única: interface de cadastro de usuário
 * Lógica separada do JSX através de props
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegisterScreen(props: IHandleRegisterScreenProps) {
    
    // Estados locais da UI
    var username by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var confirmPasswordVisible by remember { mutableStateOf(false) }
    
    // Validações
    val isFormValid = username.isNotBlank() && 
                     email.isNotBlank() && 
                     password.isNotBlank() && 
                     confirmPassword.isNotBlank() &&
                     password == confirmPassword &&
                     email.contains("@") &&
                     password.length >= 6
    
    // Layout mobile com fundo gradiente
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        AppColors.Background,
                        AppColors.Surface.copy(alpha = 0.3f)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
        ) {
            // Header com botão voltar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = props.onNavigateBack
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Voltar",
                        tint = AppColors.OnBackground,
                        modifier = Modifier.size(28.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Text(
                    text = "Criar Conta",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = AppColors.OnBackground
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Card de cadastro
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Surface
                ),
                shape = RoundedCornerShape(24.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    
                    Text(
                        text = "Junte-se à comunidade",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.SemiBold,
                        color = AppColors.OnSurface
                    )
                    
                    Text(
                        text = "Crie sua conta e comece a acompanhar seus mangás favoritos",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AppColors.OnSurfaceVariant
                    )
                    
                    // Campo de nome completo
                    OutlinedTextField(
                        value = fullName,
                        onValueChange = { fullName = it },
                        label = { Text("Nome Completo") },
                        placeholder = { Text("Como você gostaria de ser chamado?") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !props.isLoading,
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp)
                    )
                    
                    // Campo de usuário
                    OutlinedTextField(
                        value = username,
                        onValueChange = { username = it },
                        label = { Text("Nome de Usuário") },
                        placeholder = { Text("Escolha um nome único") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !props.isLoading,
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp)
                    )
                    
                    // Campo de email
                    OutlinedTextField(
                        value = email,
                        onValueChange = { email = it },
                        label = { Text("Email") },
                        placeholder = { Text("<EMAIL>") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !props.isLoading,
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                        shape = RoundedCornerShape(16.dp)
                    )
                    
                    // Campo de senha
                    OutlinedTextField(
                        value = password,
                        onValueChange = { password = it },
                        label = { Text("Senha (mín. 6 caracteres)") },
                        placeholder = { Text("Crie uma senha segura") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !props.isLoading,
                        visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        trailingIcon = {
                            IconButton(onClick = { passwordVisible = !passwordVisible }) {
                                Icon(
                                    imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                    contentDescription = if (passwordVisible) "Ocultar senha" else "Mostrar senha",
                                    tint = AppColors.OnSurfaceVariant
                                )
                            }
                        }
                    )
                    
                    // Campo de confirmar senha
                    OutlinedTextField(
                        value = confirmPassword,
                        onValueChange = { confirmPassword = it },
                        label = { Text("Confirmar Senha") },
                        placeholder = { Text("Digite a senha novamente") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !props.isLoading,
                        visualTransformation = if (confirmPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        trailingIcon = {
                            IconButton(onClick = { confirmPasswordVisible = !confirmPasswordVisible }) {
                                Icon(
                                    imageVector = if (confirmPasswordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                    contentDescription = if (confirmPasswordVisible) "Ocultar senha" else "Mostrar senha",
                                    tint = AppColors.OnSurfaceVariant
                                )
                            }
                        },
                        isError = confirmPassword.isNotBlank() && password != confirmPassword
                    )
                    
                    // Validação de senha
                    if (confirmPassword.isNotBlank() && password != confirmPassword) {
                        Text(
                            text = "As senhas não coincidem",
                            color = AppColors.Error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                    
                    // Mensagem de erro
                    props.errorMessage?.let { error ->
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = AppColors.ErrorContainer
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = error,
                                color = AppColors.OnErrorContainer,
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(16.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    
                    // Botão de cadastro
                    Button(
                        onClick = { 
                            props.onRegister(username, email, password, fullName)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        enabled = !props.isLoading && isFormValid,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AppColors.Accent,
                            contentColor = AppColors.OnAccent,
                            disabledContainerColor = AppColors.OutlineVariant,
                            disabledContentColor = AppColors.OnSurfaceVariant
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        if (props.isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = AppColors.OnAccent,
                                strokeWidth = 3.dp
                            )
                        } else {
                            Text(
                                text = "Criar Conta",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Link para voltar ao login
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Já tem uma conta? ",
                    style = MaterialTheme.typography.bodyLarge,
                    color = AppColors.OnSurfaceVariant
                )
                TextButton(
                    onClick = { props.onNavigateBack() }
                ) {
                    Text(
                        text = "Faça login",
                        style = MaterialTheme.typography.bodyLarge,
                        color = AppColors.Accent,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}
