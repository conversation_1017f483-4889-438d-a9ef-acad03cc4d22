import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import ui.App
import utils.DevUtils
import utils.Logger

/**
 * Função principal da aplicação
 * Responsabilidade única: inicializar a aplicação Compose Desktop
 */
fun main() = application {
    // Inicializa sistema de logs
    Logger.info("Iniciando aplicação...")
    
    // Imprime configurações em modo debug
    DevUtils.printConfiguration()

    Window(
        onCloseRequest = {
            Logger.info("Fechando aplicação...")
            exitApplication()
        },
        title = "App de Login - Kotlin Compose"
    ) {
        App()
    }
}
