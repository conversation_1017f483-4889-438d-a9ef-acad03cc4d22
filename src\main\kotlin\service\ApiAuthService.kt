package service

import config.ApiConfig
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import model.*
import utils.Logger

/**
 * Implementação real do serviço de autenticação usando a API
 * Responsabilidade única: gerenciar autenticação via HTTP
 * Extensível para diferentes tipos de autenticação (OAuth, JWT, etc.)
 */
class ApiAuthService(
    private val httpClient: IHandleHttpClient = HttpClientProvider.getInstance()
) : IHandleAuthService {
    
    private var currentUser: User? = null
    private var authToken: String? = null
    
    /**
     * Realiza login via API
     * @param username nome do usuário
     * @param password senha do usuário
     * @return AuthState com resultado da autenticação
     */
    override suspend fun login(username: String, password: String): AuthState {
        return try {
            val loginRequest = LoginRequest(
                username = username,
                password = password
            )
            
            Logger.httpRequest("POST", "${ApiConfig.baseUrl}${ApiConfig.Endpoints.LOGIN}", loginRequest)
            
            val response: HttpResponse = httpClient.client.post("${ApiConfig.baseUrl}${ApiConfig.Endpoints.LOGIN}") {
                contentType(ContentType.Application.Json)
                setBody(loginRequest)
            }

            
            val responseBody = response.bodyAsText()
            Logger.debug("Resposta da API: $responseBody")
            Logger.httpResponse(
                status = response.status.toString(),
                body = responseBody,
                headers = response.headers.entries().associate { it.key to it.value }
            )
            
            when (response.status) {
                HttpStatusCode.OK -> {
                    val loginResponse: LoginResponse = response.body()
                    Logger.success("Login realizado com sucesso!")
                    Logger.debug("LoginResponse parseado: $loginResponse")

                    if (loginResponse.access_token != null && loginResponse.user != null) {
                        authToken = loginResponse.access_token
                        currentUser = loginResponse.user.toDomainUser()
                        Logger.success("Usuário autenticado: ${currentUser?.username}")
                        AuthState.Authenticated(currentUser!!)
                    } else {
                        Logger.error("Resposta inválida do servidor - token ou usuário ausente")
                        AuthState.Error("Resposta inválida do servidor")
                    }
                }
                
                HttpStatusCode.Unauthorized -> {
                    Logger.warning("Credenciais inválidas")
                    AuthState.Error("Usuário ou senha inválidos")
                }
                
                HttpStatusCode.BadRequest -> {
                    try {
                        val errorResponse: ApiError = response.body()
                        val errorMessage = errorResponse.message?.joinToString(", ") ?: "Dados inválidos"
                        Logger.warning("Erro 400 - $errorMessage")
                        AuthState.Error(errorMessage)
                    } catch (e: Exception) {
                        Logger.error("Erro ao parsear resposta 400: ${e.message}")
                        AuthState.Error("Dados inválidos")
                    }
                }
                
                else -> {
                    Logger.error("Erro do servidor: ${response.status}")
                    AuthState.Error("Erro do servidor: ${response.status.description}")
                }
            }
            
        } catch (e: Exception) {
            Logger.error("Erro de conexão: ${e.message}")
            Logger.debug("Stack trace: ${e.stackTraceToString()}")
            AuthState.Error("Erro de conexão: ${e.message}")
        }
    }
    
    /**
     * Realiza logout do usuário atual
     * @return AuthState.NotAuthenticated
     */
    override fun logout(): AuthState {
        Logger.info("Fazendo logout do usuário: ${currentUser?.username ?: "N/A"}")
        currentUser = null
        authToken = null
        Logger.success("Logout realizado com sucesso")
        return AuthState.NotAuthenticated
    }
    
    /**
     * Retorna o usuário atualmente autenticado
     * @return User? usuário atual ou null se não autenticado
     */
    override fun getCurrentUser(): User? {
        Logger.debug("Consultando usuário atual: ${currentUser?.username ?: "Nenhum usuário autenticado"}")
        return currentUser
    }
    
    /**
     * Retorna o token de autenticação atual
     * @return String? token atual ou null se não autenticado
     */
    fun getAuthToken(): String? {
        return authToken
    }
    
    /**
     * Realiza registro via API
     * @param username nome do usuário
     * @param email email do usuário
     * @param password senha do usuário
     * @param fullName nome completo do usuário
     * @return AuthState com resultado do registro
     */
    override suspend fun register(username: String, email: String, password: String, fullName: String): AuthState {
        return try {
            val registerRequest = RegisterRequest(
                username = username,
                email = email,
                password = password,
                fullName = fullName
            )

            Logger.httpRequest("POST", "${ApiConfig.baseUrl}${ApiConfig.Endpoints.REGISTER}", registerRequest)

            val response: HttpResponse = httpClient.client.post("${ApiConfig.baseUrl}${ApiConfig.Endpoints.REGISTER}") {
                contentType(ContentType.Application.Json)
                setBody(registerRequest)
            }

            Logger.httpResponse(response.status.value.toString(), response.status.description)

            when (response.status) {
                HttpStatusCode.Created -> {
                    val loginResponse: LoginResponse = response.body()
                    Logger.success("Registro realizado com sucesso!")

                    if (loginResponse.access_token != null && loginResponse.user != null) {
                        authToken = loginResponse.access_token
                        currentUser = loginResponse.user.toDomainUser()
                        Logger.success("Usuário registrado e autenticado: ${currentUser?.username}")
                        AuthState.Authenticated(currentUser!!)
                    } else {
                        Logger.error("Resposta inválida do servidor - token ou usuário ausente")
                        AuthState.Error("Resposta inválida do servidor")
                    }
                }

                HttpStatusCode.BadRequest -> {
                    try {
                        val errorResponse: ApiError = response.body()
                        val errorMessage = errorResponse.message?.joinToString(", ") ?: "Dados inválidos"
                        Logger.warning("Erro 400 - $errorMessage")
                        AuthState.Error(errorMessage)
                    } catch (e: Exception) {
                        Logger.error("Erro ao parsear resposta 400: ${e.message}")
                        AuthState.Error("Dados inválidos")
                    }
                }

                HttpStatusCode.Conflict -> {
                    Logger.warning("Usuário já existe")
                    AuthState.Error("Usuário ou email já existe")
                }

                else -> {
                    Logger.error("Erro do servidor: ${response.status}")
                    AuthState.Error("Erro do servidor: ${response.status.description}")
                }
            }

        } catch (e: Exception) {
            Logger.error("Erro de conexão: ${e.message}")
            AuthState.Error("Erro de conexão: ${e.message}")
        }
    }

    /**
     * Envia email de recuperação de senha via API
     * @param email email para recuperação
     * @return Result<String> com resultado da operação
     */
    override suspend fun forgotPassword(email: String): Result<String> {
        return try {
            val forgotPasswordRequest = ForgotPasswordRequest(email = email)

            Logger.httpRequest("POST", "${ApiConfig.baseUrl}${ApiConfig.Endpoints.FORGOT_PASSWORD}", forgotPasswordRequest)

            val response: HttpResponse = httpClient.client.post("${ApiConfig.baseUrl}${ApiConfig.Endpoints.FORGOT_PASSWORD}") {
                contentType(ContentType.Application.Json)
                setBody(forgotPasswordRequest)
            }

            Logger.httpResponse(response.status.value.toString(), response.status.description)

            when (response.status) {
                HttpStatusCode.OK -> {
                    val successResponse: SuccessResponse = response.body()
                    Logger.success("Email de recuperação enviado!")
                    Result.success(successResponse.message)
                }

                HttpStatusCode.NotFound -> {
                    Logger.warning("Email não encontrado")
                    Result.failure(Exception("Email não encontrado"))
                }

                HttpStatusCode.BadRequest -> {
                    try {
                        val errorResponse: ApiError = response.body()
                        val errorMessage = errorResponse.message?.joinToString(", ") ?: "Email inválido"
                        Logger.warning("Erro 400 - $errorMessage")
                        Result.failure(Exception(errorMessage))
                    } catch (e: Exception) {
                        Logger.error("Erro ao parsear resposta 400: ${e.message}")
                        Result.failure(Exception("Email inválido"))
                    }
                }

                else -> {
                    Logger.error("Erro do servidor: ${response.status}")
                    Result.failure(Exception("Erro do servidor: ${response.status.description}"))
                }
            }

        } catch (e: Exception) {
            Logger.error("Erro de conexão: ${e.message}")
            Result.failure(Exception("Erro de conexão: ${e.message}"))
        }
    }

    /**
     * Verifica se o usuário está autenticado
     * @return Boolean true se autenticado
     */
    fun isAuthenticated(): Boolean {
        return currentUser != null && authToken != null
    }
}
