package model

/**
 * Interface para representar um usuário do sistema
 * Segue o padrão IHandleUserModel
 */
interface IHandleUserModel {
    val username: String
    val isAuthenticated: Boolean
}

/**
 * Implementação concreta do modelo de usuário
 * Responsabilidade única: representar dados do usuário
 */
data class User(
    override val username: String,
    override val isAuthenticated: Boolean = false
) : IHandleUserModel

/**
 * Estados possíveis da autenticação
 * Extensível para novos estados se necessário
 */
sealed class AuthState {
    object Loading : AuthState()
    object NotAuthenticated : AuthState()
    data class Authenticated(val user: User) : AuthState()
    data class Error(val message: String) : AuthState()
}
