package service

/**
 * Enum para tipos de serviço de autenticação
 */
enum class AuthServiceType {
    MOCK,
    API
}

/**
 * Interface para factory de serviços de autenticação
 * Segue o padrão IHandleAuthServiceFactory
 */
interface IHandleAuthServiceFactory {
    fun createAuthService(type: AuthServiceType): IHandleAuthService
}

/**
 * Factory para criar serviços de autenticação
 * Responsabilidade única: criar instâncias de serviços de autenticação
 * Extensível para novos tipos de serviços
 */
object AuthServiceFactory : IHandleAuthServiceFactory {
    
    /**
     * Cria uma instância do serviço de autenticação baseado no tipo
     * @param type tipo do serviço (MOCK ou API)
     * @return IHandleAuthService instância do serviço
     */
    override fun createAuthService(type: AuthServiceType): IHandleAuthService {
        return when (type) {
            AuthServiceType.MOCK -> MockAuthService()
            AuthServiceType.API -> ApiAuthService()
        }
    }
    
    /**
     * Cria serviço baseado em variável de ambiente ou configuração
     * Por padrão usa API, mas pode ser alterado para MOCK durante desenvolvimento
     */
    fun createDefaultAuthService(): IHandleAuthService {
        // Pode ser configurado via variável de ambiente ou arquivo de configuração
        val useApi = System.getProperty("auth.use.api", "true").toBoolean()
        
        return if (useApi) {
            createAuthService(AuthServiceType.API)
        } else {
            createAuthService(AuthServiceType.MOCK)
        }
    }
}
