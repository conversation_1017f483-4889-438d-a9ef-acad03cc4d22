import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertFalse
import kotlinx.coroutines.runBlocking
import service.MockAuthService
import model.AuthState

/**
 * Testes para o serviço de autenticação
 * Responsabilidade única: testar funcionalidades de autenticação
 */
class AuthServiceTest {
    
    private val authService = MockAuthService()
    
    @Test
    fun testLoginComCredenciaisValidas() = runBlocking {
        // Arrange
        val username = "kevin"
        val password = "123"
        
        // Act
        val result = authService.login(username, password)
        
        // Assert
        assertTrue(result is AuthState.Authenticated, "Login deve ser bem-sucedido")
        if (result is AuthState.Authenticated) {
            assertEquals(username, result.user.username, "Username deve ser correto")
            assertTrue(result.user.isAuthenticated, "Usuário deve estar autenticado")
        }
    }
    
    @Test
    fun testLoginComCredenciaisInvalidas() = runBlocking {
        // Arrange
        val username = "usuario_inexistente"
        val password = "senha_errada"
        
        // Act
        val result = authService.login(username, password)
        
        // Assert
        assertTrue(result is AuthState.Error, "Login deve falhar")
        if (result is AuthState.Error) {
            assertEquals("Usuário ou senha inválidos", result.message)
        }
    }
    
    @Test
    fun testLoginComSenhaIncorreta() = runBlocking {
        // Arrange
        val username = "kevin"
        val password = "senha_errada"
        
        // Act
        val result = authService.login(username, password)
        
        // Assert
        assertTrue(result is AuthState.Error, "Login deve falhar com senha incorreta")
    }
    
    @Test
    fun testLogout() {
        // Arrange - primeiro fazer login
        runBlocking {
            authService.login("kevin", "123")
        }
        
        // Act
        val result = authService.logout()
        
        // Assert
        assertTrue(result is AuthState.NotAuthenticated, "Logout deve limpar autenticação")
        assertEquals(null, authService.getCurrentUser(), "Usuário atual deve ser null após logout")
    }
    
    @Test
    fun testGetCurrentUserSemLogin() {
        // Act
        val currentUser = authService.getCurrentUser()
        
        // Assert
        assertEquals(null, currentUser, "Usuário atual deve ser null sem login")
    }
    
    @Test
    fun testGetCurrentUserAposLogin() = runBlocking {
        // Arrange
        authService.login("kevin", "123")
        
        // Act
        val currentUser = authService.getCurrentUser()
        
        // Assert
        assertEquals("kevin", currentUser?.username, "Usuário atual deve ser kevin")
        assertTrue(currentUser?.isAuthenticated == true, "Usuário deve estar autenticado")
    }
}
