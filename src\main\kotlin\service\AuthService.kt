package service

import model.User
import model.AuthState

/**
 * Interface para o serviço de autenticação
 * Segue o padrão IHandleAuthService
 */
interface IHandleAuthService {
    suspend fun login(username: String, password: String): AuthState
    suspend fun register(username: String, email: String, password: String, fullName: String): AuthState
    suspend fun forgotPassword(email: String): Result<String>
    fun logout(): AuthState
    fun getCurrentUser(): User?
}

/**
 * Implementação mockada do serviço de autenticação
 * Responsabilidade única: gerenciar autenticação
 * Extensível para implementações reais (API, banco de dados, etc.)
 */
class MockAuthService : IHandleAuthService {
    
    private var currentUser: User? = null
    
    // Usuários mockados para teste
    private val mockUsers = mapOf(
        "kevin" to "123"
    )
    
    /**
     * Simula login com delay para demonstrar loading
     * @param username nome do usuário
     * @param password senha do usuário
     * @return AuthState com resultado da autenticação
     */
    override suspend fun login(username: String, password: String): AuthState {
        return try {
            // Simula delay de rede
            kotlinx.coroutines.delay(1000)
            
            val storedPassword = mockUsers[username]
            
            if (storedPassword != null && storedPassword == password) {
                currentUser = User(username = username, isAuthenticated = true)
                AuthState.Authenticated(currentUser!!)
            } else {
                AuthState.Error("Usuário ou senha inválidos")
            }
        } catch (e: Exception) {
            AuthState.Error("Erro interno: ${e.message}")
        }
    }

    /**
     * Simula registro de usuário
     * @param username nome do usuário
     * @param email email do usuário
     * @param password senha do usuário
     * @param fullName nome completo do usuário
     * @return AuthState com resultado do registro
     */
    override suspend fun register(username: String, email: String, password: String, fullName: String): AuthState {
        return try {
            // Simula delay de rede
            kotlinx.coroutines.delay(1500)

            // Simula validação (usuário já existe)
            if (mockUsers.containsKey(username)) {
                AuthState.Error("Usuário já existe")
            } else {
                // Simula sucesso no registro
                currentUser = User(username = username, isAuthenticated = true)
                AuthState.Authenticated(currentUser!!)
            }
        } catch (e: Exception) {
            AuthState.Error("Erro interno: ${e.message}")
        }
    }

    /**
     * Simula envio de email de recuperação de senha
     * @param email email para recuperação
     * @return Result<String> com resultado da operação
     */
    override suspend fun forgotPassword(email: String): Result<String> {
        return try {
            // Simula delay de rede
            kotlinx.coroutines.delay(1000)

            // Simula sucesso
            Result.success("Email de recuperação enviado para $email")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Realiza logout do usuário atual
     * @return AuthState.NotAuthenticated
     */
    override fun logout(): AuthState {
        currentUser = null
        return AuthState.NotAuthenticated
    }

    /**
     * Retorna o usuário atualmente autenticado
     * @return User? usuário atual ou null se não autenticado
     */
    override fun getCurrentUser(): User? {
        return currentUser
    }
}
