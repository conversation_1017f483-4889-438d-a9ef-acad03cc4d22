package ui

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import hooks.IHandleAuthHook
import model.User

/**
 * Interface para props da tela home
 * Segue o padrão IHandleHomeScreenProps
 */
interface IHandleHomeScreenProps {
    val user: User
    val onLogout: () -> Unit
}

/**
 * Implementação das props da tela home
 */
data class HomeScreenProps(
    override val user: User,
    override val onLogout: () -> Unit
) : IHandleHomeScreenProps

/**
 * Tela Home
 * Responsabilidade única: exibir informações do usuário logado
 * Lógica separada através de props e callbacks
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(props: IHandleHomeScreenProps) {
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        
        // TopBar com informações do usuário e logout
        TopAppBar(
            title = { Text("Home") },
            actions = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "Usuário"
                    )
                    Text(
                        text = props.user.username,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    IconButton(
                        onClick = props.onLogout
                    ) {
                        Icon(
                            imageVector = Icons.Default.ExitToApp,
                            contentDescription = "Sair"
                        )
                    }
                }
            }
        )
        
        // Conteúdo principal
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    
                    // Ícone de usuário
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "Usuário",
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    // Mensagem de boas-vindas
                    Text(
                        text = "Bem-vindo, ${props.user.username}!",
                        style = MaterialTheme.typography.headlineMedium
                    )

                    Text(
                        text = "Você está logado com sucesso.",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    
                    // Informações do usuário
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "Informações do Usuário",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text("Usuário:")
                                Text(props.user.username)
                            }
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text("Status:")
                                Text(
                                    text = if (props.user.isAuthenticated) "Autenticado" else "Não autenticado",
                                    color = if (props.user.isAuthenticated)
                                        MaterialTheme.colorScheme.primary
                                    else
                                        MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                    
                    // Botão de logout
                    OutlinedButton(
                        onClick = props.onLogout,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.ExitToApp,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Sair")
                    }
                }
            }
        }
    }
}
