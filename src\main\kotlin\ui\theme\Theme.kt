package ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * Cores personalizadas para o tema mobile manga/manhwa
 * Responsabilidade única: definir paleta de cores em tons de cinza
 */
object AppColors {
    // Cores primárias - tons de cinza com accent sutil
    val Primary = Color(0xFF6B7280)        // Cinza médio elegante
    val PrimaryVariant = Color(0xFF4B5563) // Cinza mais escuro
    val OnPrimary = Color(0xFFFFFFFF)      // Branco puro

    // Cores secundárias - tons de cinza neutros
    val Secondary = Color(0xFF9CA3AF)      // Cinza claro
    val SecondaryVariant = Color(0xFF6B7280) // Cinza médio
    val OnSecondary = Color(0xFF111827)    // Quase preto

    // Cores de fundo - tons escuros para mobile
    val Background = Color(0xFF111827)     // Preto suave (fundo principal)
    val Surface = Color(0xFF1F2937)       // Cinza escuro (cards)
    val SurfaceVariant = Color(0xFF374151) // Cinza médio (elementos)
    val OnBackground = Color(0xFFF9FAFB)  // Branco suave
    val OnSurface = Color(0xFFF3F4F6)     // Branco acinzentado
    val OnSurfaceVariant = Color(0xFFD1D5DB) // Cinza claro

    // Cores de erro - vermelho suave
    val Error = Color(0xFFEF4444)
    val OnError = Color(0xFFFFFFFF)
    val ErrorContainer = Color(0xFF7F1D1D)
    val OnErrorContainer = Color(0xFFFECACA)

    // Cores de sucesso - verde suave
    val Success = Color(0xFF10B981)
    val OnSuccess = Color(0xFFFFFFFF)
    val SuccessContainer = Color(0xFF064E3B)
    val OnSuccessContainer = Color(0xFFA7F3D0)

    // Cores de warning - amarelo suave
    val Warning = Color(0xFFF59E0B)
    val OnWarning = Color(0xFF000000)
    val WarningContainer = Color(0xFF78350F)
    val OnWarningContainer = Color(0xFFFED7AA)

    // Accent para elementos especiais (manga/manhwa theme)
    val Accent = Color(0xFF8B5CF6)         // Roxo suave para destaques
    val AccentVariant = Color(0xFF7C3AED) // Roxo mais intenso
    val OnAccent = Color(0xFFFFFFFF)

    // Outline e divisores
    val Outline = Color(0xFF4B5563)       // Cinza médio para bordas
    val OutlineVariant = Color(0xFF374151) // Cinza escuro para divisores

    // Cores específicas para manga/manhwa
    val ReadingProgress = Color(0xFF10B981) // Verde para progresso
    val Bookmark = Color(0xFFF59E0B)       // Amarelo para favoritos
    val Rating = Color(0xFFEAB308)         // Dourado para avaliações
}

/**
 * Esquema de cores mobile manga/manhwa
 */
private val MangaColorScheme = darkColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryVariant,
    onPrimaryContainer = AppColors.OnPrimary,

    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryVariant,
    onSecondaryContainer = AppColors.OnSecondary,

    tertiary = AppColors.Accent,
    onTertiary = AppColors.OnAccent,
    tertiaryContainer = AppColors.AccentVariant,
    onTertiaryContainer = AppColors.OnAccent,

    background = AppColors.Background,
    onBackground = AppColors.OnBackground,

    surface = AppColors.Surface,
    onSurface = AppColors.OnSurface,
    surfaceVariant = AppColors.SurfaceVariant,
    onSurfaceVariant = AppColors.OnSurfaceVariant,

    error = AppColors.Error,
    onError = AppColors.OnError,
    errorContainer = AppColors.ErrorContainer,
    onErrorContainer = AppColors.OnErrorContainer,

    outline = AppColors.Outline,
    outlineVariant = AppColors.OutlineVariant
)

/**
 * Esquema de cores claro (fallback)
 */
private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF1976D2),
    onPrimary = Color(0xFFFFFFFF),
    background = Color(0xFFFFFBFE),
    surface = Color(0xFFFFFBFE),
    onBackground = Color(0xFF1C1B1F),
    onSurface = Color(0xFF1C1B1F),
)

/**
 * Tema principal da aplicação mobile manga/manhwa
 * Responsabilidade única: aplicar tema consistente
 * Extensível para diferentes variações de tema
 */
@Composable
fun MangaTrackerTheme(
    content: @Composable () -> Unit
) {
    MaterialTheme(
        colorScheme = MangaColorScheme,
        typography = Typography(),
        content = content
    )
}

/**
 * Alias para compatibilidade
 */
@Composable
fun AppTheme(
    darkTheme: Boolean = true,
    content: @Composable () -> Unit
) {
    MangaTrackerTheme(content = content)
}
