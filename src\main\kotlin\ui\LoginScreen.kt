package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import hooks.IHandleAuthHook

// Modern color scheme
object ModernColors {
    val background = Color(0xFF0F0C29)          // Fundo
    val backgroundSecondary = Color(0xFF1C1C2E)  // Fundo secundário
    val primary = Color(0xFF9D4EDD)             // Primária
    val primaryHover = Color(0xFF7B2CBF)        // Hover primária
    val textLight = Color(0xFFF8F8F8)           // Texto claro
    val textWeak = Color(0xFFA0A0B0)            // Texto fraco
    val inputField = Color(0xFF2D2D44)          // Campo input
    val borderLight = Color(0xFF414167)         // Borda leve
}

/**
 * Componente para ícone de rede social minimalista
 */
@Composable
fun SocialIcon(
    icon: ImageVector,
    contentDescription: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(56.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(ModernColors.inputField)
            .border(1.dp, ModernColors.borderLight, RoundedCornerShape(16.dp))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = ModernColors.textLight,
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * Interface para props da tela de login
 * Segue o padrão IHandleLoginScreenProps
 */
interface IHandleLoginScreenProps {
    val authHook: IHandleAuthHook
    val onNavigateToRegister: () -> Unit
    val onNavigateToForgotPassword: () -> Unit
}

/**
 * Implementação das props da tela de login
 */
data class LoginScreenProps(
    override val authHook: IHandleAuthHook,
    override val onNavigateToRegister: () -> Unit = {},
    override val onNavigateToForgotPassword: () -> Unit = {}
) : IHandleLoginScreenProps

/**
 * Tela de Login - Design moderno e clean
 * Responsabilidade única: interface de autenticação
 * Lógica separada do JSX através do hook
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(props: IHandleLoginScreenProps) {

    // Estados locais da UI (separados da lógica de negócio)
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }

    // Lógica de negócio delegada ao hook
    val authHook = props.authHook

    // Layout moderno e clean sem card
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(ModernColors.background)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header da aplicação
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Logo/Ícone moderno
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(RoundedCornerShape(24.dp))
                        .background(
                            Brush.linearGradient(
                                colors = listOf(
                                    ModernColors.primary,
                                    ModernColors.primaryHover
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Filled.MenuBook,
                        contentDescription = "Manga Tracker Logo",
                        tint = ModernColors.textLight,
                        modifier = Modifier.size(40.dp)
                    )
                }

                Text(
                    text = "Manga Tracker",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontSize = 32.sp,
                        fontWeight = FontWeight.Bold,
                        letterSpacing = (-1).sp
                    ),
                    color = ModernColors.textLight
                )

                Text(
                    text = "Acompanhe e avalie seus mangás, manhwas e manhuas favoritos.",
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Normal
                    ),
                    color = ModernColors.textWeak,
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(48.dp))
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                Text(
                    text = "Fazer login",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontSize = 28.sp,
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = ModernColors.textLight
                )

           OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = { 
                        Text(
                            "Email ou usuário",
                            color = ModernColors.textWeak
                        ) 
                    },
                    placeholder = { 
                        Text(
                            "Digite seu email",
                            color = ModernColors.textWeak.copy(alpha = 0.7f)
                        ) 
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Person,
                            contentDescription = "Usuário",
                            tint = ModernColors.primary
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(64.dp),
                    enabled = !authHook.isLoading,
                    singleLine = true,
                    shape = RoundedCornerShape(16.dp)
                )

                // Campo de senha
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { 
                        Text(
                            "Senha",
                            color = ModernColors.textWeak
                        ) 
                    },
                    placeholder = { 
                        Text(
                            "Digite sua senha",
                            color = ModernColors.textWeak.copy(alpha = 0.7f)
                        ) 
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Lock,
                            contentDescription = "Senha",
                            tint = ModernColors.primary
                        )
                    },
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "Ocultar senha" else "Mostrar senha",
                                tint = ModernColors.primary
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(64.dp),
                    enabled = !authHook.isLoading,
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,
                    shape = RoundedCornerShape(16.dp)
                )

                // Mensagem de erro
                authHook.errorMessage?.let { error ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                            .background(
                                Color(0xFF7F1D1D).copy(alpha = 0.1f),
                                RoundedCornerShape(12.dp)
                            )
                            .border(
                                1.dp,
                                Color(0xFFEF4444).copy(alpha = 0.3f),
                                RoundedCornerShape(12.dp)
                            )
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.Warning,
                            contentDescription = "Erro",
                            tint = Color(0xFFEF4444),
                            modifier = Modifier.size(20.dp)
                        )
                        Text(
                            text = error,
                            color = Color(0xFFFECACA),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                // Link "Esqueci minha senha" alinhado à direita
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = { props.onNavigateToForgotPassword() }
                    ) {
                        Text(
                            text = "Esqueci minha senha",
                            color = ModernColors.primary,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }
                }

                // Botão de login
                Button(
                    onClick = {
                        authHook.clearError()
                        authHook.login(username, password)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !authHook.isLoading && username.isNotBlank() && password.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ModernColors.primary,
                        contentColor = ModernColors.textLight,
                        disabledContainerColor = ModernColors.inputField,
                        disabledContentColor = ModernColors.textWeak
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    if (authHook.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = ModernColors.textLight,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Entrar",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp
                            )
                        )
                    }
                }

                // Divider com "ou continuar com"
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Divider(
                        modifier = Modifier.weight(1f),
                        color = ModernColors.borderLight
                    )
                    Text(
                        text = "ou continuar com",
                        color = ModernColors.textWeak,
                        style = MaterialTheme.typography.bodySmall
                    )
                    Divider(
                        modifier = Modifier.weight(1f),
                        color = ModernColors.borderLight
                    )
                }

                // Ícones de redes sociais em linha
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    SocialIcon(
                        icon = Icons.Filled.Email,
                        contentDescription = "Login com Google",
                        onClick = { /* TODO: Implementar Google Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.Facebook,
                        contentDescription = "Login com Facebook",
                        onClick = { /* TODO: Implementar Facebook Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.AlternateEmail,
                        contentDescription = "Login com Twitter/X",
                        onClick = { /* TODO: Implementar Twitter Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.Code,
                        contentDescription = "Login com GitHub",
                        onClick = { /* TODO: Implementar GitHub Auth */ }
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Link para cadastro
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Não tem uma conta? ",
                    style = MaterialTheme.typography.bodyMedium,
                    color = ModernColors.textWeak
                )
                TextButton(
                    onClick = { props.onNavigateToRegister() }
                ) {
                    Text(
                        text = "Cadastre-se",
                        color = ModernColors.primary,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
        }
    }
}
