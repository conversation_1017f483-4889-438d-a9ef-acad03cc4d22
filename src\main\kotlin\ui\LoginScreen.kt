package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import hooks.IHandleAuthHook

// Modern gray color scheme for manga/manhwa tracker
object ModernColors {
    val background = Color(0xFF0F0F0F)          // Fundo principal - cinza muito escuro
    val backgroundSecondary = Color(0xFF1A1A1A) // Fundo secundário - cinza escuro
    val surface = Color(0xFF262626)             // Superfície - cinza médio
    val surfaceVariant = Color(0xFF333333)      // Variante de superfície
    val primary = Color(0xFF6B7280)             // Primária - cinza azulado
    val primaryHover = Color(0xFF4B5563)        // Hover primária - cinza azulado escuro
    val accent = Color(0xFF9CA3AF)              // Accent - cinza claro
    val textPrimary = Color(0xFFF9FAFB)         // Texto principal - quase branco
    val textSecondary = Color(0xFFD1D5DB)       // Texto secundário - cinza claro
    val textTertiary = Color(0xFF9CA3AF)        // Texto terciário - cinza médio
    val inputField = Color(0xFF1F2937)          // Campo input - cinza escuro
    val inputBorder = Color(0xFF374151)         // Borda input - cinza
    val inputBorderFocused = Color(0xFF6B7280)  // Borda input focado
    val error = Color(0xFFEF4444)               // Erro - vermelho
    val errorBackground = Color(0xFF1F1F1F)     // Fundo erro
    val success = Color(0xFF10B981)             // Sucesso - verde
    val divider = Color(0xFF374151)             // Divisor
}

/**
 * Componente para ícone de rede social minimalista com tema manga/manhwa
 */
@Composable
fun SocialIcon(
    icon: ImageVector,
    contentDescription: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(56.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(ModernColors.surface)
            .border(1.dp, ModernColors.inputBorder, RoundedCornerShape(12.dp))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = ModernColors.textSecondary,
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * Interface para props da tela de login
 * Segue o padrão IHandleLoginScreenProps
 */
interface IHandleLoginScreenProps {
    val authHook: IHandleAuthHook
    val onNavigateToRegister: () -> Unit
    val onNavigateToForgotPassword: () -> Unit
}

/**
 * Implementação das props da tela de login
 */
data class LoginScreenProps(
    override val authHook: IHandleAuthHook,
    override val onNavigateToRegister: () -> Unit = {},
    override val onNavigateToForgotPassword: () -> Unit = {}
) : IHandleLoginScreenProps

/**
 * Tela de Login - Design moderno e clean
 * Responsabilidade única: interface de autenticação
 * Lógica separada do JSX através do hook
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(props: IHandleLoginScreenProps) {

    // Estados locais da UI (separados da lógica de negócio)
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }

    // Lógica de negócio delegada ao hook
    val authHook = props.authHook

    // Layout moderno e clean com gradiente sutil
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        ModernColors.background,
                        ModernColors.backgroundSecondary
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header da aplicação com tema manga/manhwa
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Logo/Ícone moderno com tema manga
                Box(
                    modifier = Modifier
                        .size(88.dp)
                        .clip(RoundedCornerShape(22.dp))
                        .background(
                            Brush.linearGradient(
                                colors = listOf(
                                    ModernColors.primary,
                                    ModernColors.primaryHover
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Filled.MenuBook,
                        contentDescription = "Manga Tracker Logo",
                        tint = ModernColors.textPrimary,
                        modifier = Modifier.size(44.dp)
                    )
                }

                Text(
                    text = "Manga Tracker",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontSize = 28.sp,
                        fontWeight = FontWeight.ExtraBold,
                        letterSpacing = (-0.5).sp
                    ),
                    color = ModernColors.textPrimary
                )

                Text(
                    text = "Acompanhe sua jornada através dos mundos dos mangás e manhwas",
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 15.sp,
                        fontWeight = FontWeight.Normal,
                        lineHeight = 22.sp
                    ),
                    color = ModernColors.textTertiary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(40.dp))

            // Formulário de login
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                Text(
                    text = "Entrar na sua conta",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    color = ModernColors.textPrimary
                )

                // Campo de usuário/email
                OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = {
                        Text(
                            "Email ou usuário",
                            color = ModernColors.textTertiary
                        )
                    },
                    placeholder = {
                        Text(
                            "Digite seu email ou usuário",
                            color = ModernColors.textTertiary.copy(alpha = 0.6f)
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Person,
                            contentDescription = "Usuário",
                            tint = ModernColors.accent
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    enabled = !authHook.isLoading,
                    singleLine = true,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = ModernColors.inputBorderFocused,
                        unfocusedBorderColor = ModernColors.inputBorder,
                        focusedLabelColor = ModernColors.accent,
                        unfocusedLabelColor = ModernColors.textTertiary,
                        focusedTextColor = ModernColors.textPrimary,
                        unfocusedTextColor = ModernColors.textSecondary,
                        cursorColor = ModernColors.accent,
                        containerColor = ModernColors.inputField
                    )
                )

                // Campo de senha
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = {
                        Text(
                            "Senha",
                            color = ModernColors.textTertiary
                        )
                    },
                    placeholder = {
                        Text(
                            "Digite sua senha",
                            color = ModernColors.textTertiary.copy(alpha = 0.6f)
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Lock,
                            contentDescription = "Senha",
                            tint = ModernColors.accent
                        )
                    },
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "Ocultar senha" else "Mostrar senha",
                                tint = ModernColors.accent
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    enabled = !authHook.isLoading,
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = ModernColors.inputBorderFocused,
                        unfocusedBorderColor = ModernColors.inputBorder,
                        focusedLabelColor = ModernColors.accent,
                        unfocusedLabelColor = ModernColors.textTertiary,
                        focusedTextColor = ModernColors.textPrimary,
                        unfocusedTextColor = ModernColors.textSecondary,
                        cursorColor = ModernColors.accent,
                        containerColor = ModernColors.inputField
                    )
                )

                // Mensagem de erro com design moderno
                authHook.errorMessage?.let { error ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                ModernColors.errorBackground,
                                RoundedCornerShape(10.dp)
                            )
                            .border(
                                1.dp,
                                ModernColors.error.copy(alpha = 0.3f),
                                RoundedCornerShape(10.dp)
                            )
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.Warning,
                            contentDescription = "Erro",
                            tint = ModernColors.error,
                            modifier = Modifier.size(18.dp)
                        )
                        Text(
                            text = error,
                            color = ModernColors.textSecondary,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontSize = 14.sp
                            )
                        )
                    }
                }

                // Link "Esqueci minha senha" alinhado à direita
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = { props.onNavigateToForgotPassword() }
                    ) {
                        Text(
                            text = "Esqueci minha senha",
                            color = ModernColors.accent,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp
                            )
                        )
                    }
                }

                // Botão de login moderno
                Button(
                    onClick = {
                        authHook.clearError()
                        authHook.login(username, password)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(52.dp),
                    enabled = !authHook.isLoading && username.isNotBlank() && password.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ModernColors.primary,
                        contentColor = ModernColors.textPrimary,
                        disabledContainerColor = ModernColors.surface,
                        disabledContentColor = ModernColors.textTertiary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (authHook.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = ModernColors.textPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Entrar",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp
                            )
                        )
                    }
                }

                // Divider com "ou continuar com" - design moderno
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Divider(
                        modifier = Modifier.weight(1f),
                        color = ModernColors.divider,
                        thickness = 1.dp
                    )
                    Text(
                        text = "ou continuar com",
                        color = ModernColors.textTertiary,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontSize = 13.sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                    Divider(
                        modifier = Modifier.weight(1f),
                        color = ModernColors.divider,
                        thickness = 1.dp
                    )
                }

                // Ícones de redes sociais em linha
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    SocialIcon(
                        icon = Icons.Filled.Email,
                        contentDescription = "Login com Google",
                        onClick = { /* TODO: Implementar Google Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.Facebook,
                        contentDescription = "Login com Facebook",
                        onClick = { /* TODO: Implementar Facebook Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.AlternateEmail,
                        contentDescription = "Login com Twitter/X",
                        onClick = { /* TODO: Implementar Twitter Auth */ }
                    )
                    
                    SocialIcon(
                        icon = Icons.Filled.Code,
                        contentDescription = "Login com GitHub",
                        onClick = { /* TODO: Implementar GitHub Auth */ }
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Link para cadastro com design moderno
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Não tem uma conta? ",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    color = ModernColors.textTertiary
                )
                TextButton(
                    onClick = { props.onNavigateToRegister() }
                ) {
                    Text(
                        text = "Cadastre-se",
                        color = ModernColors.accent,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp
                        )
                    )
                }
            }
        }
    }
}
