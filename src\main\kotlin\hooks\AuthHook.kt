package hooks

import androidx.compose.runtime.*
import kotlinx.coroutines.launch
import model.AuthState
import model.User
import service.IHandleAuthService

/**
 * Interface para o hook de autenticação
 * Segue o padrão IHandleAuthHook
 */
interface IHandleAuthHook {
    val authState: AuthState
    val isLoading: Boolean
    val errorMessage: String?
    fun login(username: String, password: String)
    fun logout()
    fun clearError()
}

/**
 * Hook para gerenciar estado de autenticação
 * Responsabilidade única: gerenciar estado e ações de autenticação
 * Extensível para diferentes tipos de autenticação
 */
@Composable
fun useAuth(authService: IHandleAuthService): IH<PERSON>leAuthHook {
    
    var authState by remember { mutableStateOf<AuthState>(AuthState.NotAuthenticated) }
    val coroutineScope = rememberCoroutineScope()
    
    // Estados derivados para facilitar uso na UI
    val isLoading = authState is AuthState.Loading
    val errorMessage = (authState as? AuthState.Error)?.message
    
    return object : IHandleAuthHook {
        override val authState: AuthState = authState
        override val isLoading: Boolean = isLoading
        override val errorMessage: String? = errorMessage
        
        override fun login(username: String, password: String) {
            coroutineScope.launch {
                authState = AuthState.Loading
                authState = authService.login(username, password)
            }
        }
        
        override fun logout() {
            authState = authService.logout()
        }
        
        override fun clearError() {
            if (authState is AuthState.Error) {
                authState = AuthState.NotAuthenticated
            }
        }
    }
}
