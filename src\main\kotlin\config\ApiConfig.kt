package config

/**
 * Interface para configuração da API
 * Segue o padrão IHandleApiConfig
 */
interface IHandleApiConfig {
    val baseUrl: String
    val timeout: Long
    val retryAttempts: Int
}

/**
 * Configuração da API
 * Responsabilidade única: centralizar configurações de rede
 * Extensível para diferentes ambientes (dev, prod, test)
 */
object ApiConfig : IHandleApiConfig {
    override val baseUrl: String = "http://localhost:3000"
    override val timeout: Long = 30_000L // 30 segundos
    override val retryAttempts: Int = 3
    
    // Endpoints específicos
    object Endpoints {
        const val LOGIN = "/auth/login"
        const val REGISTER = "/auth/register"
        const val REFRESH = "/auth/refresh"
        const val PROFILE = "/auth/profile"
        const val CHANGE_PASSWORD = "/auth/change-password"
        const val FORGOT_PASSWORD = "/auth/forgot-password"
        const val RESET_PASSWORD = "/auth/reset-password"
    }
}
