import kotlinx.coroutines.runBlocking
import model.AuthState
import service.ApiAuthService
import service.AuthServiceFactory
import service.AuthServiceType
import kotlin.test.Test
import kotlin.test.assertTrue

/**
 * Testes para o serviço de autenticação via API
 * Responsabilidade única: testar funcionalidades de autenticação com API real
 */
class ApiAuthServiceTest {
    
    @Test
    fun testFactoryCreatesCorrectService() {
        // Arrange & Act
        val mockService = AuthServiceFactory.createAuthService(AuthServiceType.MOCK)
        val apiService = AuthServiceFactory.createAuthService(AuthServiceType.API)
        
        // Assert
        assertTrue(mockService::class.simpleName == "MockAuthService")
        assertTrue(apiService::class.simpleName == "ApiAuthService")
    }
    
    @Test
    fun testApiAuthServiceInitialization() {
        // Arrange & Act
        val authService = ApiAuthService()
        
        // Assert
        assertTrue(authService.getCurrentUser() == null)
        assertTrue(authService.getAuthToken() == null)
        assertTrue(!authService.isAuthenticated())
    }
    
    // Nota: Testes de integração com API real devem ser executados
    // apenas quando o backend estiver rodando em localhost:3000
    // Para testes automatizados, considere usar um mock server
}
