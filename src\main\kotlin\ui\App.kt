package ui

import androidx.compose.runtime.*
import hooks.useAuth
import model.AuthState
import service.AuthServiceFactory
import ui.theme.AppTheme
import kotlinx.coroutines.launch

/**
 * Enum para controlar a navegação entre telas
 */
enum class Screen {
    LOGIN,
    REGISTER,
    FORGOT_PASSWORD,
    HOME
}

/**
 * Componente principal da aplicação
 * Responsabilidade única: gerenciar navegação entre telas baseada no estado de autenticação
 * Extensível para adicionar novas telas e rotas
 */
@Composable
fun App() {

    // Serviço de autenticação (injeção de dependência)
    // Usa factory para escolher entre mock e API real
    val authService = remember { AuthServiceFactory.createDefaultAuthService() }

    // Hook de autenticação (lógica separada do JSX)
    val authHook = useAuth(authService)

    // Estado de navegação
    var currentScreen by remember { mutableStateOf(Screen.LOGIN) }

    // Estados para registro e recuperação de senha
    var isRegisterLoading by remember { mutableStateOf(false) }
    var registerError by remember { mutableStateOf<String?>(null) }
    var isForgotPasswordLoading by remember { mutableStateOf(false) }
    var forgotPasswordError by remember { mutableStateOf<String?>(null) }
    var forgotPasswordSuccess by remember { mutableStateOf<String?>(null) }

    val coroutineScope = rememberCoroutineScope()

    // Tema da aplicação
    AppTheme {

        // Navegação baseada no estado de autenticação
        when (val state = authHook.authState) {
            is AuthState.Authenticated -> {
                // Exibe tela home
                HomeScreen(
                    props = HomeScreenProps(
                        user = state.user,
                        onLogout = {
                            authHook.logout()
                            currentScreen = Screen.LOGIN
                        }
                    )
                )
            }

            else -> {
                // Exibe telas de autenticação baseado na navegação
                when (currentScreen) {
                    Screen.LOGIN -> {
                        LoginScreen(
                            props = LoginScreenProps(
                                authHook = authHook,
                                onNavigateToRegister = {
                                    currentScreen = Screen.REGISTER
                                    registerError = null
                                },
                                onNavigateToForgotPassword = {
                                    currentScreen = Screen.FORGOT_PASSWORD
                                    forgotPasswordError = null
                                    forgotPasswordSuccess = null
                                }
                            )
                        )
                    }

                    Screen.REGISTER -> {
                        RegisterScreen(
                            props = RegisterScreenProps(
                                onNavigateBack = {
                                    currentScreen = Screen.LOGIN
                                    registerError = null
                                },
                                onRegister = { username, email, password, fullName ->
                                    coroutineScope.launch {
                                        isRegisterLoading = true
                                        registerError = null

                                        val result = authService.register(username, email, password, fullName)

                                        when (result) {
                                            is AuthState.Authenticated -> {
                                                // Registro bem-sucedido, usuário já está logado
                                                currentScreen = Screen.HOME
                                            }
                                            is AuthState.Error -> {
                                                registerError = result.message
                                            }
                                            else -> {
                                                registerError = "Erro inesperado"
                                            }
                                        }

                                        isRegisterLoading = false
                                    }
                                },
                                isLoading = isRegisterLoading,
                                errorMessage = registerError
                            )
                        )
                    }

                    Screen.FORGOT_PASSWORD -> {
                        ForgotPasswordScreen(
                            props = ForgotPasswordScreenProps(
                                onNavigateBack = {
                                    currentScreen = Screen.LOGIN
                                    forgotPasswordError = null
                                    forgotPasswordSuccess = null
                                },
                                onSendResetEmail = { email ->
                                    coroutineScope.launch {
                                        isForgotPasswordLoading = true
                                        forgotPasswordError = null
                                        forgotPasswordSuccess = null

                                        val result = authService.forgotPassword(email)

                                        result.fold(
                                            onSuccess = { message ->
                                                forgotPasswordSuccess = message
                                            },
                                            onFailure = { error ->
                                                forgotPasswordError = error.message ?: "Erro desconhecido"
                                            }
                                        )

                                        isForgotPasswordLoading = false
                                    }
                                },
                                isLoading = isForgotPasswordLoading,
                                errorMessage = forgotPasswordError,
                                successMessage = forgotPasswordSuccess
                            )
                        )
                    }

                    Screen.HOME -> {
                        // Não deveria chegar aqui, mas por segurança
                        currentScreen = Screen.LOGIN
                    }
                }
            }
        }
    }
}
