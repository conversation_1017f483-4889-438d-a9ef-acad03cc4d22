package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Email
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import ui.theme.AppColors

/**
 * Interface para propriedades da tela de recuperação de senha
 * Segue o padrão IHandleForgotPasswordScreenProps
 */
interface IHandleForgotPasswordScreenProps {
    val onNavigateBack: () -> Unit
    val onSendResetEmail: (email: String) -> Unit
    val isLoading: Boolean
    val errorMessage: String?
    val successMessage: String?
}

/**
 * Implementação das propriedades da tela de recuperação de senha
 */
data class ForgotPasswordScreenProps(
    override val onNavigateBack: () -> Unit = {},
    override val onSendResetEmail: (email: String) -> Unit = { _ -> },
    override val isLoading: Boolean = false,
    override val errorMessage: String? = null,
    override val successMessage: String? = null
) : IHandleForgotPasswordScreenProps

/**
 * Tela de Recuperação de Senha
 * Responsabilidade única: interface para solicitar reset de senha
 * Lógica separada do JSX através de props
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForgotPasswordScreen(props: IHandleForgotPasswordScreenProps) {
    
    // Estados locais da UI
    var email by remember { mutableStateOf("") }
    
    // Validação
    val isEmailValid = email.isNotBlank() && email.contains("@")
    
    // Layout da tela com gradiente de fundo
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        AppColors.Background,
                        AppColors.Surface
                    )
                )
            )
    ) {
        // Botão de voltar
        IconButton(
            onClick = props.onNavigateBack,
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.TopStart)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Voltar",
                tint = AppColors.OnBackground
            )
        }
        
        // Conteúdo principal
        Card(
            modifier = Modifier
                .width(420.dp)
                .padding(24.dp)
                .align(Alignment.Center),
            elevation = CardDefaults.cardElevation(defaultElevation = 12.dp),
            colors = CardDefaults.cardColors(
                containerColor = AppColors.Surface
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                
                // Ícone e título
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Email,
                        contentDescription = "Email",
                        modifier = Modifier.size(64.dp),
                        tint = AppColors.Primary
                    )
                    
                    Text(
                        text = "Esqueceu sua senha?",
                        style = MaterialTheme.typography.headlineLarge,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.OnSurface,
                        textAlign = TextAlign.Center
                    )
                    
                    Text(
                        text = "Digite seu email e enviaremos um link para redefinir sua senha",
                        style = MaterialTheme.typography.bodyLarge,
                        color = AppColors.OnSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
                
                // Campo de email
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email") },
                    placeholder = { Text("<EMAIL>") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),

                    shape = RoundedCornerShape(12.dp),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Email,
                            contentDescription = "Email",
                            tint = AppColors.OnSurfaceVariant
                        )
                    }
                )
                
                // Mensagem de sucesso
                props.successMessage?.let { message ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = AppColors.SuccessContainer
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = message,
                            color = AppColors.OnSuccessContainer,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(12.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                // Mensagem de erro
                props.errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = AppColors.ErrorContainer
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = error,
                            color = AppColors.OnErrorContainer,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(12.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                // Botão de enviar
                Button(
                    onClick = { 
                        props.onSendResetEmail(email)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !props.isLoading && isEmailValid,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = AppColors.Primary,
                        contentColor = AppColors.OnPrimary,
                        disabledContainerColor = AppColors.OutlineVariant,
                        disabledContentColor = AppColors.OnSurfaceVariant
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (props.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = AppColors.OnPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Enviar Link de Recuperação",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }
                
                // Link para voltar ao login
                Text(
                    text = "Voltar ao login",
                    style = MaterialTheme.typography.bodyMedium,
                    color = AppColors.Primary,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.clickable { props.onNavigateBack() },
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
