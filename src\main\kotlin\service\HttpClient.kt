package service

import config.ApiConfig
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json

/**
 * Interface para cliente HTTP
 * Segue o padrão IHandleHttpClient
 */
interface IHandleHttpClient {
    val client: HttpClient
    fun close()
}

/**
 * Cliente HTTP configurado para comunicação com a API
 * Responsabilidade única: configurar e fornecer cliente HTTP
 * Extensível para diferentes configurações e interceptadores
 */
class ApiHttpClient(
    private val config: ApiConfig = ApiConfig
) : IHandleHttpClient {
    
    override val client: HttpClient = HttpClient(CIO) {
        
        // Configuração de timeout
        install(HttpTimeout) {
            requestTimeoutMillis = config.timeout
            connectTimeoutMillis = config.timeout
            socketTimeoutMillis = config.timeout
        }
        
        // Configuração de retry
        install(HttpRequestRetry) {
            retryOnServerErrors(maxRetries = config.retryAttempts)
            exponentialDelay()
        }
        
        // Configuração de serialização JSON
        install(ContentNegotiation) {
            json(Json {
                prettyPrint = true
                isLenient = true
                ignoreUnknownKeys = true
            })
        }
        
        // Configuração de logging (apenas em desenvolvimento)
        install(Logging) {
            logger = Logger.DEFAULT
            level = LogLevel.INFO
        }
        
        // Configuração de headers padrão
        defaultRequest {
            headers.append("Content-Type", "application/json")
            headers.append("Accept", "application/json")
        }
    }
    
    /**
     * Fecha o cliente HTTP
     * Importante para liberar recursos
     */
    override fun close() {
        client.close()
    }
}

/**
 * Singleton para reutilização do cliente HTTP
 * Evita criar múltiplas instâncias desnecessariamente
 */
object HttpClientProvider {
    private var _instance: IHandleHttpClient? = null
    
    fun getInstance(): IHandleHttpClient {
        return _instance ?: synchronized(this) {
            _instance ?: ApiHttpClient().also { _instance = it }
        }
    }
    
    fun closeInstance() {
        _instance?.close()
        _instance = null
    }
}
