package utils

import service.AuthServiceType
import service.AuthServiceFactory

/**
 * Interface para utilitários de desenvolvimento
 * Segue o padrão IHandleDevUtils
 */
interface IHandleDevUtils {
    fun isDebugMode(): Boolean
    fun shouldUseMockServices(): Boolean
    fun getApiBaseUrl(): String
}

/**
 * Utilitários para desenvolvimento e configuração
 * Responsabilidade única: fornecer configurações de desenvolvimento
 * Extensível para diferentes ambientes
 */
object DevUtils : IHandleDevUtils {
    
    /**
     * Verifica se está em modo debug
     */
    override fun isDebugMode(): Boolean {
        return System.getProperty("debug", "false").toBoolean()
    }
    
    /**
     * Verifica se deve usar serviços mock
     */
    override fun shouldUseMockServices(): Boolean {
        return System.getProperty("use.mock", "false").toBoolean()
    }
    
    /**
     * Retorna a URL base da API
     */
    override fun getApiBaseUrl(): String {
        return System.getProperty("api.base.url", "http://localhost:3000")
    }
    
    /**
     * Configura o tipo de serviço de autenticação baseado no ambiente
     */
    fun getAuthServiceType(): AuthServiceType {
        return if (shouldUseMockServices()) {
            AuthServiceType.MOCK
        } else {
            AuthServiceType.API
        }
    }
    
    /**
     * Imprime informações de configuração no console
     */
    fun printConfiguration() {
        if (isDebugMode()) {
            println("=== Configuração da Aplicação ===")
            println("Debug Mode: ${isDebugMode()}")
            println("Use Mock Services: ${shouldUseMockServices()}")
            println("API Base URL: ${getApiBaseUrl()}")
            println("Auth Service Type: ${getAuthServiceType()}")
            println("================================")
        }
    }
}

/**
 * Logger para desenvolvimento com diferentes níveis de log
 */
object Logger {
    
    enum class LogLevel(val emoji: String, val color: String) {
        DEBUG("🔍", "\u001B[36m"),    // Cyan
        INFO("ℹ️", "\u001B[34m"),      // Blue
        SUCCESS("✅", "\u001B[32m"),   // Green
        WARNING("⚠️", "\u001B[33m"),   // Yellow
        ERROR("❌", "\u001B[31m"),     // Red
        HTTP("🌐", "\u001B[35m")       // Magenta
    }
    
    private const val RESET = "\u001B[0m"
    
    fun debug(message: String) {
        if (DevUtils.isDebugMode()) {
            log(LogLevel.DEBUG, message)
        }
    }
    
    fun info(message: String) {
        log(LogLevel.INFO, message)
    }
    
    fun success(message: String) {
        log(LogLevel.SUCCESS, message)
    }
    
    fun warning(message: String) {
        log(LogLevel.WARNING, message)
    }
    
    fun error(message: String) {
        log(LogLevel.ERROR, message)
    }
    
    fun http(message: String) {
        if (DevUtils.isDebugMode()) {
            log(LogLevel.HTTP, message)
        }
    }
    
    private fun log(level: LogLevel, message: String) {
        val timestamp = java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")
        )
        println("${level.color}[${level.name}] ${level.emoji} [$timestamp] $message$RESET")
    }
    
    fun httpRequest(method: String, url: String, body: Any? = null) {
        if (DevUtils.isDebugMode()) {
            http("$method $url")
            body?.let { http("📤 Body: $it") }
        }
    }
    
    fun httpResponse(status: String, body: String? = null, headers: Map<String, List<String>>? = null) {
        if (DevUtils.isDebugMode()) {
            http("📥 Status: $status")
            headers?.let { http("📥 Headers: $it") }
            body?.let { http("📋 Body: $it") }
        }
    }
}
