package model

import kotlinx.serialization.Serializable

/**
 * Modelos de dados para comunicação com a API
 * Responsabilidade única: representar estruturas de dados da API
 */

/**
 * Interface para requisição de login
 * Segue o padrão IHandleLoginRequest
 */
interface IHandleLoginRequest {
    val username: String
    val password: String
}

/**
 * Requisição de login
 * Baseado na documentação da API: {"username": "joao_silva", "password": "MinhaSenh@123"}
 */
@Serializable
data class LoginRequest(
    override val username: String,
    override val password: String
) : IHandleLoginRequest

/**
 * Interface para resposta de login
 * Segue o padrão IHandleLoginResponse
 */
interface IHandleLoginResponse {
    val access_token: String?
    val refresh_token: String?
    val token_type: String?
    val expires_in: Int?
    val user: ApiUser?
}

/**
 * Resposta de login da API
 */
@Serializable
data class LoginResponse(
    override val access_token: String? = null,
    override val refresh_token: String? = null,
    override val token_type: String? = null,
    override val expires_in: Int? = null,
    override val user: ApiUser? = null
) : IHandleLoginResponse

/**
 * Interface para usuário da API
 * Segue o padrão IHandleApiUser
 */
interface IHandleApiUser {
    val id: Int?
    val username: String
    val email: String?
    val fullName: String?
    val avatar: String?
    val isActive: Boolean?
    val createdAt: String?
    val updatedAt: String?
}

/**
 * Modelo de usuário retornado pela API
 */
@Serializable
data class ApiUser(
    override val id: Int? = null,
    override val username: String,
    override val email: String? = null,
    override val fullName: String? = null,
    override val avatar: String? = null,
    override val isActive: Boolean? = null,
    override val createdAt: String? = null,
    override val updatedAt: String? = null
) : IHandleApiUser

/**
 * Interface para resposta de erro da API
 * Segue o padrão IHandleApiError
 */
interface IHandleApiError {
    val statusCode: Int?
    val message: List<String>?
    val error: String?
    val timestamp: String?
    val path: String?
}

/**
 * Resposta de erro da API
 */
@Serializable
data class ApiError(
    override val statusCode: Int? = null,
    override val message: List<String>? = null,
    override val error: String? = null,
    override val timestamp: String? = null,
    override val path: String? = null
) : IHandleApiError

/**
 * Interface para requisição de registro
 * Segue o padrão IHandleRegisterRequest
 */
interface IHandleRegisterRequest {
    val username: String
    val email: String
    val password: String
    val fullName: String?
}

/**
 * Requisição de registro
 */
@Serializable
data class RegisterRequest(
    override val username: String,
    override val email: String,
    override val password: String,
    override val fullName: String? = null
) : IHandleRegisterRequest

/**
 * Interface para requisição de recuperação de senha
 * Segue o padrão IHandleForgotPasswordRequest
 */
interface IHandleForgotPasswordRequest {
    val email: String
}

/**
 * Requisição de recuperação de senha
 */
@Serializable
data class ForgotPasswordRequest(
    override val email: String
) : IHandleForgotPasswordRequest

/**
 * Interface para resposta de sucesso genérica
 * Segue o padrão IHandleSuccessResponse
 */
interface IHandleSuccessResponse {
    val message: String
    val success: Boolean
}

/**
 * Resposta de sucesso genérica
 */
@Serializable
data class SuccessResponse(
    override val message: String,
    override val success: Boolean = true
) : IHandleSuccessResponse

/**
 * Extensão para converter ApiUser em User do domínio
 */
fun ApiUser.toDomainUser(): User {
    return User(
        username = this.username,
        isAuthenticated = true
    )
}
